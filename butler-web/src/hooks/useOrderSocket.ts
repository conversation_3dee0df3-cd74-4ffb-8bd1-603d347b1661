/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { useSocket } from "@/contexts/SocketContext";
import { Order, Payment } from "@/types/order";
import { playNotificationSound } from "@/utils/notificationSound";

export const useOrderSocket = (orderId?: string) => {
  const { customerSocket, adminSocket } = useSocket();
  const [order, setOrder] = useState<Order | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [socket, setSocket] = useState<any>(null);

  // Function to update orders optimistically
  const updateOrderOptimistically = (orderId: string, updates: any) => {
    setOrders((prev) =>
      prev.map((order) => {
        if (order._id === orderId) {
          const updatedOrder = { ...order };

          // Apply updates
          Object.keys(updates).forEach((key) => {
            if (key.includes(".")) {
              // Handle nested updates like "items.0.isServed"
              const [parentKey, index, childKey] = key.split(".");
              if (
                parentKey === "items" &&
                updatedOrder.items[parseInt(index)]
              ) {
                updatedOrder.items[parseInt(index)] = {
                  ...updatedOrder.items[parseInt(index)],
                  [childKey]: updates[key],
                };
              }
            } else {
              // Handle direct updates like "status"
              (updatedOrder as any)[key] = updates[key];
            }
          });

          return updatedOrder;
        }
        return order;
      })
    );
  };

  useEffect(() => {
    const isAdmin = !!localStorage.getItem("auth-token");
    const userToken = localStorage.getItem("user-token");
    console.log("isAdmin", localStorage.getItem("auth-token"));
    console.log("userToken exists", !!userToken);
    console.log("orderId", orderId);
    console.log("customerSocket", !!customerSocket);

    if (isAdmin) {
      setSocket(adminSocket);
    } else {
      setSocket(customerSocket);
    }

    if (!isAdmin && orderId && customerSocket) {
      setLoading(true);
      console.log("Attempting to join order room for orderId:", orderId);

      // Add error handling for socket connection
      customerSocket.on("connect", () => {
        console.log("Customer socket connected, joining order room");
        customerSocket.emit("join-order", orderId);
      });

      // If already connected, emit join-order immediately
      if (customerSocket.connected) {
        console.log("Customer socket already connected, joining order room");
        customerSocket.emit("join-order", orderId);
      }

      customerSocket.on("order-status-update", (data: { data: Order }) => {
        console.log("Received order status update:", data);
        setOrder(data.data);
        setLoading(false);
      });

      // Listen for payment updates
      customerSocket.on(
        "payment-update",
        (data: { type: string; data: { order: Order; payment: Payment } }) => {
          console.log("Received payment update:", data);
          if (data.data.order) {
            setOrder(data.data.order);
          }
        }
      );

      // Listen for errors
      customerSocket.on("error", (error: any) => {
        console.error("Customer socket error:", error);
        setLoading(false);
      });

      // Add timeout to handle cases where no response is received
      const timeout = setTimeout(() => {
        console.warn("No order status update received within 10 seconds");
        setLoading(false);
      }, 10000);

      return () => {
        clearTimeout(timeout);
        if (customerSocket) {
          customerSocket.emit("leave-order", orderId);
          customerSocket.off("order-status-update");
          customerSocket.off("payment-update");
          customerSocket.off("connect");
          customerSocket.off("error");
        }
      };
    }

    if (isAdmin && adminSocket) {
      console.log("Admin socket connected");
      setLoading(true);

      adminSocket.on("connect", () => {
        console.log("Admin socket connected event");
        // Force refresh of orders when socket connects
        adminSocket.emit("refresh-orders");
      });

      // Listen for initial orders
      adminSocket.on("initial-orders", (data: { orders: Order[] }) => {
        console.log("Admin received initial orders:", data);
        if (data && data.orders) {
          setOrders(data.orders);
          setLoading(false);
        } else {
          console.error("Received invalid initial orders data:", data);
        }
      });

      // Listen for new orders
      adminSocket.on("new-order", (data: { type: string; data: Order }) => {
        console.log("Admin received new order:", data);
        if (data && data.data) {
          // Add the new order to the beginning of the list
          setOrders((prev) => [data.data, ...prev]);

          // Play notification sound
          playNotificationSound("new-order");

          // Show a notification
          if (window.Notification && Notification.permission === "granted") {
            new Notification("New Order", {
              body: `New order #${data.data.orderNumber} received`,
            });
          }
        } else {
          console.error("Received invalid new order data:", data);
        }
      });

      // Listen for order updates
      adminSocket.on("order-status-update", (data: { data: Order }) => {
        console.log("Admin received order update:", data);
        setOrders((prev) =>
          prev.map((order) => (order._id === data.data._id ? data.data : order))
        );
      });

      // Listen for payment updates
      adminSocket.on(
        "payment-update",
        (data: { type: string; data: { order: Order; payment: Payment } }) => {
          console.log("Admin received payment update:", data);
          if (data.data.order) {
            setOrders((prev) =>
              prev.map((order) =>
                order._id === data.data.order._id ? data.data.order : order
              )
            );
          }
        }
      );

      return () => {
        if (adminSocket) {
          adminSocket.off("initial-orders");
          adminSocket.off("new-order");
          adminSocket.off("order-status-update");
          adminSocket.off("payment-update");
          adminSocket.off("connect");
        }
      };
    }
  }, [orderId, customerSocket, adminSocket]);

  return {
    order,
    orders,
    loading,
    socket,
    updateOrderOptimistically,
  };
};
