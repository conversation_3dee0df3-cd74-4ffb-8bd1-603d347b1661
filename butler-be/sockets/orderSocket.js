import { verifyToken } from "../middlewares/auth.js";
import Order from "../models/Order.js";
import Payment from "../models/Payment.js";

export const setupOrderSocket = (io) => {
  const adminNamespace = io.of("/admin-orders");
  const customerNamespace = io.of("/customer-orders");

  // Customer namespace setup
  customerNamespace.use(async (socket, next) => {
    try {
      // For customer sockets, we're more lenient - no strict auth required
      // but we log connection attempts for debugging
      console.log(
        "Customer socket connection attempt",
        socket.handshake.auth ? "with auth" : "without auth"
      );
      next();
    } catch (error) {
      console.error("Customer socket error:", error.message);
      next(new Error(`Connection error: ${error.message}`));
    }
  });

  customerNamespace.on("connection", (socket) => {
    console.log("Customer connected to socket");

    socket.on("join-order", async (orderId) => {
      console.log("Customer joining order room:", orderId);
      if (!orderId) {
        console.error("No order ID provided for join-order event");
        socket.emit("error", { message: "No order ID provided" });
        return;
      }

      // Validate orderId format
      if (!orderId.match(/^[0-9a-fA-F]{24}$/)) {
        console.error("Invalid order ID format:", orderId);
        socket.emit("error", { message: "Invalid order ID format" });
        return;
      }

      socket.join(`order-${orderId}`);
      console.log(`Customer joined order room: order-${orderId}`);

      // Send initial order status
      try {
        const order = await Order.findById(orderId)
          .populate("items.dishId", "name price")
          .populate("userId", "name phone email")
          .populate("outletId", "name address contact");

        if (order) {
          console.log(`Sending order data for order ID: ${orderId}`);
          socket.emit("order-status-update", {
            type: "order-status-update",
            data: order,
          });
        } else {
          console.error("Order not found for ID:", orderId);
          socket.emit("error", { message: "Order not found" });
        }
      } catch (error) {
        console.error("Error fetching initial order:", error);
        socket.emit("error", {
          message: "Error fetching order data",
          details: error.message,
        });
      }
    });

    socket.on("leave-order", (orderId) => {
      if (orderId) {
        socket.leave(`order-${orderId}`);
        console.log("Customer left order room:", orderId);
      }
    });

    socket.on("disconnect", () => {
      console.log("Customer disconnected from socket");
    });

    socket.on("error", (error) => {
      console.error("Customer socket error:", error);
    });
  });

  // Admin namespace setup
  adminNamespace.use(async (socket, next) => {
    try {
      console.log("Admin socket auth attempt", socket.handshake.auth);
      const token = socket.handshake.auth.token;
      if (!token) {
        console.error("No token provided in socket handshake");
        return next(new Error("Authentication error: No token provided"));
      }

      const decoded = verifyToken(token);
      console.log("Token decoded:", decoded ? "success" : "failed");

      if (!decoded) {
        console.error("Invalid token in socket handshake");
        return next(new Error("Authentication error: Invalid token"));
      }

      if (decoded.role !== "admin") {
        console.error("Unauthorized role in socket handshake:", decoded.role);
        return next(new Error("Unauthorized: Admin role required"));
      }

      socket.user = decoded;
      next();
    } catch (error) {
      console.error("Socket authentication error:", error.message);
      next(new Error(`Authentication error: ${error.message}`));
    }
  });

  adminNamespace.on("connection", async (socket) => {
    try {
      console.log("Admin connected:", socket.user.foodChain);

      // Subscribe admin to their food chain's room
      socket.join(`foodchain-${socket.user.foodChain}`);
      console.log(
        `Admin joined foodchain room: foodchain-${socket.user.foodChain}`
      );

      // Send initial orders for the admin's food chain
      try {
        const orders = await Order.find({ foodChainId: socket.user.foodChain })
          .sort({ createdAt: -1 })
          .limit(50)
          .populate("items.dishId", "name price")
          .populate("userId", "name phone email")
          .populate("outletId", "name address contact");

        console.log(`Sending ${orders.length} initial orders to admin`);
        socket.emit("initial-orders", { orders });
      } catch (error) {
        console.error("Error fetching initial orders:", error);
        socket.emit("error", { message: "Error fetching initial orders" });
      }

      socket.on("subscribe-outlet", (outletId) => {
        if (outletId) {
          socket.join(`outlet-${outletId}`);
          console.log(`Admin subscribed to outlet: outlet-${outletId}`);
        } else {
          console.error("No outlet ID provided for subscribe-outlet event");
        }
      });

      // Handle refresh-orders event
      socket.on("refresh-orders", async () => {
        try {
          console.log(
            `Admin requested order refresh for foodchain: ${socket.user.foodChain}`
          );

          // Fetch latest orders for the admin's food chain
          const orders = await Order.find({
            foodChainId: socket.user.foodChain,
          })
            .sort({ createdAt: -1 })
            .limit(50)
            .populate("items.dishId", "name price")
            .populate("userId", "name phone email")
            .populate("outletId", "name address contact");

          console.log(`Sending ${orders.length} refreshed orders to admin`);
          socket.emit("initial-orders", { orders });
        } catch (error) {
          console.error("Error refreshing orders:", error);
          socket.emit("error", { message: "Error refreshing orders" });
        }
      });

      socket.on("disconnect", () => {
        console.log("Admin disconnected from socket");
      });

      socket.on("error", (error) => {
        console.error("Admin socket error:", error);
      });
    } catch (error) {
      console.error("Error in admin socket connection handler:", error);
      socket.emit("error", { message: "Internal server error" });
    }
  });
};

export const emitNewOrder = async (orderId) => {
  try {
    console.log(`Emitting new order event for order ID: ${orderId}`);
    const order = await Order.findById(orderId)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) {
      console.error(`Order not found for ID: ${orderId}`);
      return;
    }

    const io = global.io;
    if (!io) {
      console.error("Global io object not available");
      return;
    }

    const adminNamespace = io.of("/admin-orders");
    console.log(`Emitting to foodchain-${order.foodChainId} room`);

    // Get connected clients in the food chain room
    const foodChainRoom = adminNamespace.adapter.rooms.get(
      `foodchain-${order.foodChainId}`
    );
    console.log(
      `Connected clients in foodchain-${order.foodChainId} room:`,
      foodChainRoom ? foodChainRoom.size : 0
    );

    // REMOVED: Broadcasting to all admin clients regardless of food chain
    // This was causing orders to appear in the wrong food chains
    // console.log("Broadcasting new order to all admin clients");
    // adminNamespace.emit("new-order", {
    //   type: "new-order",
    //   data: order,
    // });

    // Emit to food chain room
    adminNamespace.to(`foodchain-${order.foodChainId}`).emit("new-order", {
      type: "new-order",
      data: order,
    });

    // Also emit to specific outlet room
    adminNamespace.to(`outlet-${order.outletId._id}`).emit("new-order", {
      type: "new-order",
      data: order,
    });

    console.log(
      `Successfully emitted new order event for order ID: ${orderId}`
    );
  } catch (error) {
    console.error("Error emitting new order:", error);
  }
};

export const emitOrderStatusUpdate = async (orderId) => {
  try {
    const order = await Order.findById(orderId)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) return;

    const io = global.io;
    if (!io) return;

    // Emit to customer
    const customerNamespace = io.of("/customer-orders");
    customerNamespace.to(`order-${orderId}`).emit("order-status-update", {
      type: "order-status-update",
      data: order,
    });

    // Emit to admin (both food chain and outlet rooms)
    const adminNamespace = io.of("/admin-orders");
    adminNamespace
      .to(`foodchain-${order.foodChainId}`)
      .emit("order-status-update", {
        type: "order-status-update",
        data: order,
      });
    adminNamespace
      .to(`outlet-${order.outletId._id}`)
      .emit("order-status-update", {
        type: "order-status-update",
        data: order,
      });
  } catch (error) {
    console.error("Error emitting order status update:", error);
  }
};

export const emitPaymentUpdate = async (orderId) => {
  try {
    console.log(`Emitting payment update for order ID: ${orderId}`);
    const order = await Order.findById(orderId)
      .populate("items.dishId", "name price")
      .populate("userId", "name phone email")
      .populate("outletId", "name address contact");

    if (!order) {
      console.error(`Order not found for payment update: ${orderId}`);
      return;
    }

    // Get payment details if available
    const payment = await Payment.findOne({ orderId });
    console.log(
      `Payment found for order ${orderId}:`,
      payment ? payment._id : "none"
    );

    const io = global.io;
    if (!io) {
      console.error("Global io object not available for payment update");
      return;
    }

    // Prepare data to emit
    const dataToEmit = {
      order,
      payment: payment || null,
    };

    // Emit to customer
    const customerNamespace = io.of("/customer-orders");
    customerNamespace.to(`order-${orderId}`).emit("payment-update", {
      type: "payment-update",
      data: dataToEmit,
    });
    console.log(`Emitted payment update to customer for order: ${orderId}`);

    // Emit to admin (both food chain and outlet rooms)
    const adminNamespace = io.of("/admin-orders");

    // Get connected clients in the food chain room for debugging
    const foodChainRoom = adminNamespace.adapter.rooms.get(
      `foodchain-${order.foodChainId}`
    );
    console.log(
      `Emitting payment update to foodchain-${order.foodChainId} room with ${
        foodChainRoom ? foodChainRoom.size : 0
      } connected clients`
    );

    adminNamespace.to(`foodchain-${order.foodChainId}`).emit("payment-update", {
      type: "payment-update",
      data: dataToEmit,
    });

    adminNamespace.to(`outlet-${order.outletId._id}`).emit("payment-update", {
      type: "payment-update",
      data: dataToEmit,
    });
    console.log(`Payment update emitted successfully for order: ${orderId}`);
  } catch (error) {
    console.error("Error emitting payment update:", error);
  }
};
